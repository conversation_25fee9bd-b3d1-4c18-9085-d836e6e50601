#!/usr/bin/env python3
"""
Comprehensive Database Seeding Script for Allora Sustainable E-commerce Platform
================================================================================

This script seeds the entire database with realistic data for a sustainable e-commerce platform.
It creates:
- 100+ sustainable products across 10 categories
- 50+ users with realistic profiles
- 20+ sellers with sustainable business profiles
- 200+ orders with realistic purchase patterns
- 500+ product reviews and ratings
- Community posts, comments, and engagement data
- Complete supporting data (categories, coupons, shipping, etc.)

Usage:
    python seed_database.py

Requirements:
    - Flask app context
    - Database connection configured
    - All models imported from app.py
"""

import os
import sys
import random
import json
from datetime import datetime, timedelta
from decimal import Decimal
from faker import Faker
import hashlib

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Initialize Faker for generating realistic data
fake = Faker()

# Sustainable product categories and their characteristics
SUSTAINABLE_CATEGORIES = {
    'Eco-Friendly Home': {
        'description': 'Sustainable home products made from recycled and eco-friendly materials',
        'keywords': ['bamboo', 'recycled', 'biodegradable', 'organic', 'sustainable'],
        'price_range': (15, 200),
        'sustainability_score': (8, 10)
    },
    'Organic Fashion': {
        'description': 'Ethically made clothing from organic and sustainable materials',
        'keywords': ['organic cotton', 'hemp', 'fair trade', 'ethical', 'sustainable fashion'],
        'price_range': (25, 300),
        'sustainability_score': (7, 10)
    },
    'Green Electronics': {
        'description': 'Energy-efficient and eco-friendly electronic devices',
        'keywords': ['energy efficient', 'solar powered', 'recyclable', 'low carbon'],
        'price_range': (50, 800),
        'sustainability_score': (6, 9)
    },
    'Natural Beauty': {
        'description': 'Organic and cruelty-free beauty and personal care products',
        'keywords': ['organic', 'cruelty-free', 'natural', 'vegan', 'chemical-free'],
        'price_range': (10, 150),
        'sustainability_score': (8, 10)
    },
    'Sustainable Sports': {
        'description': 'Eco-friendly sports and outdoor equipment',
        'keywords': ['recycled materials', 'sustainable', 'eco-friendly', 'renewable'],
        'price_range': (20, 400),
        'sustainability_score': (7, 9)
    },
    'Zero Waste Living': {
        'description': 'Products to help achieve a zero-waste lifestyle',
        'keywords': ['zero waste', 'reusable', 'compostable', 'plastic-free'],
        'price_range': (5, 100),
        'sustainability_score': (9, 10)
    },
    'Organic Food': {
        'description': 'Organic and locally sourced food products',
        'keywords': ['organic', 'local', 'non-GMO', 'pesticide-free', 'sustainable farming'],
        'price_range': (8, 80),
        'sustainability_score': (8, 10)
    },
    'Eco Baby & Kids': {
        'description': 'Safe and sustainable products for babies and children',
        'keywords': ['non-toxic', 'organic', 'safe', 'sustainable', 'eco-friendly'],
        'price_range': (12, 200),
        'sustainability_score': (9, 10)
    },
    'Green Garden': {
        'description': 'Sustainable gardening and plant care products',
        'keywords': ['organic fertilizer', 'sustainable', 'biodegradable', 'eco-friendly'],
        'price_range': (10, 150),
        'sustainability_score': (8, 10)
    },
    'Renewable Energy': {
        'description': 'Solar panels, wind power, and renewable energy solutions',
        'keywords': ['solar', 'wind', 'renewable', 'clean energy', 'sustainable'],
        'price_range': (100, 2000),
        'sustainability_score': (9, 10)
    }
}

def hash_password(password):
    """Hash password using SHA-256"""
    return hashlib.sha256(password.encode()).hexdigest()

def random_date_between(start_date, end_date):
    """Generate random date between two dates"""
    time_between = end_date - start_date
    days_between = time_between.days
    random_days = random.randrange(days_between)
    return start_date + timedelta(days=random_days)

def random_datetime_between(start_date, end_date):
    """Generate random datetime between two dates"""
    time_between = end_date - start_date
    seconds_between = time_between.total_seconds()
    random_seconds = random.randrange(int(seconds_between))
    return start_date + timedelta(seconds=random_seconds)

class DatabaseSeeder:
    """Main class for seeding the database with sustainable e-commerce data"""
    
    def __init__(self):
        self.db = None
        self.models = {}
        self.created_data = {
            'categories': [],
            'users': [],
            'sellers': [],
            'admin_users': [],
            'products': [],
            'orders': [],
            'reviews': [],
            'posts': [],
            'hashtags': [],
            'shipping_zones': [],
            'shipping_methods': [],
            'coupons': [],
            'tax_rates': [],
            'payment_gateways': [],
            'oauth_providers': [],
            'shipping_carriers': []
        }
        
    def initialize_models(self):
        """Import all database models"""
        try:
            from app import (
                db, Category, Product, User, Seller, AdminUser, Order, OrderItem,
                ProductReview, ProductImage, ProductVariant, CartItem, Wishlist,
                CommunityPost, PostComment, PostLike, Hashtag, PostHashtag,
                UserAddress, PaymentMethod, ShippingZone, ShippingMethod,
                Coupon, CouponUsage, TaxRate, PaymentGateway, PaymentTransaction,
                Invoice, Refund, UserInteractionLog, UserBehaviorProfile,
                UserSession, Sales, PriceHistory, CommunityStats, OAuthProvider,
                UserOAuth, GuestSession, SavedCart, AbandonedCart, EmailNotification,
                RecentlyViewed, AvailabilityNotification, ProductComparison,
                SupportTicket, SupportMessage, SupportAttachment, Banner,
                ContentPage, NewsletterSubscription, SearchAnalytics,
                VisualSearchAnalytics, CookieConsent, SellerStore, SellerCommission,
                SellerPayout, ShippingCarrier, Shipment, TrackingEvent,
                FulfillmentRule, CarrierRate, RMARequest, RMAItem, RMATimeline,
                RMADocument, ReturnShipment, RMAApproval, RMARule, RMAConfiguration,
                RMAStats, InventoryLog, SalesChannel, ChannelInventory,
                InventorySyncLog, InventoryConflict, SyncQueue, SimpleProduct,
                SimpleUser, SimpleOrder, SimpleSeller, TestTable, ChatSession,
                ChatMessage, SearchClick, SearchConversion, CommunityInsight,
                CookieConsentHistory, CookieAuditLog, DataExportRequest
            )
            
            self.db = db
            self.models = {
                'Category': Category, 'Product': Product, 'User': User, 'Seller': Seller,
                'AdminUser': AdminUser, 'Order': Order, 'OrderItem': OrderItem,
                'ProductReview': ProductReview, 'ProductImage': ProductImage,
                'ProductVariant': ProductVariant, 'CartItem': CartItem, 'Wishlist': Wishlist,
                'CommunityPost': CommunityPost, 'PostComment': PostComment, 'PostLike': PostLike,
                'Hashtag': Hashtag, 'PostHashtag': PostHashtag, 'UserAddress': UserAddress,
                'PaymentMethod': PaymentMethod, 'ShippingZone': ShippingZone,
                'ShippingMethod': ShippingMethod, 'Coupon': Coupon, 'CouponUsage': CouponUsage,
                'TaxRate': TaxRate, 'PaymentGateway': PaymentGateway,
                'PaymentTransaction': PaymentTransaction, 'Invoice': Invoice, 'Refund': Refund,
                'UserInteractionLog': UserInteractionLog, 'UserBehaviorProfile': UserBehaviorProfile,
                'UserSession': UserSession, 'Sales': Sales, 'PriceHistory': PriceHistory,
                'CommunityStats': CommunityStats, 'OAuthProvider': OAuthProvider,
                'UserOAuth': UserOAuth, 'GuestSession': GuestSession, 'SavedCart': SavedCart,
                'AbandonedCart': AbandonedCart, 'EmailNotification': EmailNotification,
                'RecentlyViewed': RecentlyViewed, 'AvailabilityNotification': AvailabilityNotification,
                'ProductComparison': ProductComparison, 'SupportTicket': SupportTicket,
                'SupportMessage': SupportMessage, 'SupportAttachment': SupportAttachment,
                'Banner': Banner, 'ContentPage': ContentPage, 'NewsletterSubscription': NewsletterSubscription,
                'SearchAnalytics': SearchAnalytics, 'VisualSearchAnalytics': VisualSearchAnalytics,
                'CookieConsent': CookieConsent, 'SellerStore': SellerStore,
                'SellerCommission': SellerCommission, 'SellerPayout': SellerPayout,
                'ShippingCarrier': ShippingCarrier, 'Shipment': Shipment, 'TrackingEvent': TrackingEvent,
                'FulfillmentRule': FulfillmentRule, 'CarrierRate': CarrierRate,
                'RMARequest': RMARequest, 'RMAItem': RMAItem, 'RMATimeline': RMATimeline,
                'RMADocument': RMADocument, 'ReturnShipment': ReturnShipment,
                'RMAApproval': RMAApproval, 'RMARule': RMARule, 'RMAConfiguration': RMAConfiguration,
                'RMAStats': RMAStats, 'InventoryLog': InventoryLog, 'SalesChannel': SalesChannel,
                'ChannelInventory': ChannelInventory, 'InventorySyncLog': InventorySyncLog,
                'InventoryConflict': InventoryConflict, 'SyncQueue': SyncQueue,
                'SimpleProduct': SimpleProduct, 'SimpleUser': SimpleUser,
                'SimpleOrder': SimpleOrder, 'SimpleSeller': SimpleSeller, 'TestTable': TestTable,
                'ChatSession': ChatSession, 'ChatMessage': ChatMessage,
                'SearchClick': SearchClick, 'SearchConversion': SearchConversion,
                'CommunityInsight': CommunityInsight, 'CookieConsentHistory': CookieConsentHistory,
                'CookieAuditLog': CookieAuditLog, 'DataExportRequest': DataExportRequest
            }
            
            print("✅ Successfully imported all database models")
            return True

        except ImportError as e:
            print(f"❌ Failed to import models: {e}")
            return False

# Sustainable product names and descriptions
SUSTAINABLE_PRODUCTS = {
    'Eco-Friendly Home': [
        ('Bamboo Cutting Board Set', 'Set of 3 sustainable bamboo cutting boards with antimicrobial properties'),
        ('Recycled Glass Water Bottles', 'Beautiful water bottles made from 100% recycled glass'),
        ('Organic Cotton Bed Sheets', 'Luxurious bed sheets made from certified organic cotton'),
        ('Cork Yoga Mat', 'Natural cork yoga mat with excellent grip and sustainability'),
        ('Bamboo Kitchen Utensil Set', 'Complete kitchen utensil set made from sustainable bamboo'),
        ('Recycled Plastic Storage Containers', 'Durable storage containers made from ocean plastic'),
        ('Hemp Rope Basket Set', 'Stylish storage baskets woven from natural hemp fibers'),
        ('Biodegradable Cleaning Sponges', 'Plant-based cleaning sponges that decompose naturally'),
        ('Solar-Powered LED String Lights', 'Beautiful outdoor lighting powered by renewable solar energy'),
        ('Reclaimed Wood Picture Frames', 'Rustic picture frames crafted from reclaimed barn wood'),
        ('Organic Wool Throw Blanket', 'Cozy throw blanket made from ethically sourced organic wool'),
        ('Bamboo Fiber Towel Set', 'Ultra-soft towels made from sustainable bamboo fiber'),
        ('Recycled Metal Wall Art', 'Modern wall art created from upcycled metal materials'),
        ('Natural Beeswax Candles', 'Hand-poured candles made from pure, sustainable beeswax'),
        ('Cork Coaster Set', 'Elegant coasters made from renewable cork material')
    ],
    'Organic Fashion': [
        ('Organic Cotton T-Shirt', 'Comfortable t-shirt made from 100% certified organic cotton'),
        ('Hemp Denim Jeans', 'Durable jeans crafted from sustainable hemp and organic cotton blend'),
        ('Fair Trade Wool Sweater', 'Cozy sweater made from ethically sourced merino wool'),
        ('Recycled Polyester Jacket', 'Weather-resistant jacket made from recycled plastic bottles'),
        ('Organic Linen Dress', 'Elegant dress made from sustainably grown organic linen'),
        ('Bamboo Fiber Socks', 'Moisture-wicking socks made from antibacterial bamboo fiber'),
        ('Tencel Modal Underwear', 'Soft underwear made from sustainably sourced Tencel modal'),
        ('Cork Leather Handbag', 'Stylish handbag made from sustainable cork leather alternative'),
        ('Organic Cotton Pajama Set', 'Comfortable sleepwear made from certified organic cotton'),
        ('Recycled Wool Scarf', 'Warm scarf crafted from upcycled wool materials'),
        ('Hemp Canvas Sneakers', 'Casual sneakers with hemp canvas upper and recycled rubber sole'),
        ('Organic Silk Blouse', 'Luxurious blouse made from ethically produced organic silk'),
        ('Sustainable Activewear Set', 'Performance wear made from recycled ocean plastic'),
        ('Fair Trade Cotton Hoodie', 'Comfortable hoodie supporting fair trade cotton farmers'),
        ('Linen Blend Trousers', 'Professional trousers made from sustainable linen blend')
    ],
    'Green Electronics': [
        ('Solar Power Bank', 'Portable charger powered by high-efficiency solar panels'),
        ('Energy-Efficient LED Monitor', '24-inch monitor with 90% energy savings and recyclable materials'),
        ('Bamboo Wireless Keyboard', 'Ergonomic keyboard with sustainable bamboo casing'),
        ('Solar-Powered Bluetooth Speaker', 'Waterproof speaker with integrated solar charging'),
        ('Recycled Plastic Phone Case', 'Protective phone case made from ocean plastic waste'),
        ('Wind-Up Emergency Radio', 'Hand-crank radio requiring no batteries or electricity'),
        ('Energy Star Smart Thermostat', 'Programmable thermostat reducing energy consumption by 23%'),
        ('Solar Garden Light Set', 'Decorative outdoor lighting powered by renewable solar energy'),
        ('Eco-Friendly Laptop Stand', 'Adjustable laptop stand made from sustainable bamboo'),
        ('Recycled Aluminum Tablet Stand', 'Sleek tablet stand crafted from 100% recycled aluminum'),
        ('Solar-Powered Security Camera', 'Wireless security camera with integrated solar panel'),
        ('Energy-Efficient Smart Bulbs', 'LED smart bulbs using 80% less energy than traditional bulbs'),
        ('Biodegradable Phone Screen Protector', 'Screen protection that decomposes naturally'),
        ('Solar-Powered Car Charger', 'Vehicle charger powered by dashboard solar panel'),
        ('Recycled Plastic Cable Organizer', 'Cable management solution made from upcycled materials')
    ],
    'Natural Beauty': [
        ('Organic Argan Oil Serum', 'Pure argan oil serum for natural skin hydration and repair'),
        ('Bamboo Charcoal Face Mask', 'Detoxifying face mask with activated bamboo charcoal'),
        ('Vegan Lip Balm Set', 'Nourishing lip balms made with organic plant-based ingredients'),
        ('Natural Deodorant Stick', 'Aluminum-free deodorant with organic coconut oil and shea butter'),
        ('Organic Rose Hip Oil', 'Anti-aging facial oil rich in vitamins and antioxidants'),
        ('Cruelty-Free Makeup Brush Set', 'Professional brushes with synthetic bristles and bamboo handles'),
        ('Himalayan Salt Body Scrub', 'Exfoliating scrub with pure Himalayan pink salt and organic oils'),
        ('Organic Shampoo Bar', 'Zero-waste shampoo bar with natural cleansing ingredients'),
        ('Vegan Nail Polish Set', 'Non-toxic nail polish in beautiful natural colors'),
        ('Natural Clay Face Cleanser', 'Gentle cleanser with French green clay and organic botanicals'),
        ('Organic Coconut Oil Moisturizer', 'Rich moisturizer with virgin coconut oil and vitamin E'),
        ('Cruelty-Free Mascara', 'Lengthening mascara with natural waxes and plant-based pigments'),
        ('Natural Sunscreen SPF 30', 'Mineral sunscreen with zinc oxide and organic ingredients'),
        ('Organic Essential Oil Set', 'Pure essential oils for aromatherapy and natural wellness'),
        ('Bamboo Makeup Remover Pads', 'Reusable makeup remover pads made from soft bamboo fiber')
    ],
    'Sustainable Sports': [
        ('Recycled Yoga Mat', 'Non-slip yoga mat made from recycled rubber and natural materials'),
        ('Organic Cotton Gym Towel', 'Absorbent workout towel made from certified organic cotton'),
        ('Bamboo Water Bottle', 'Insulated water bottle with sustainable bamboo exterior'),
        ('Recycled Plastic Dumbbells', 'Adjustable dumbbells made from recycled ocean plastic'),
        ('Hemp Resistance Bands', 'Durable exercise bands made from natural hemp fibers'),
        ('Cork Massage Ball Set', 'Self-massage balls made from sustainable cork material'),
        ('Organic Cotton Yoga Blocks', 'Supportive yoga blocks with organic cotton covers'),
        ('Recycled Foam Roller', 'Muscle recovery roller made from recycled foam materials'),
        ('Sustainable Running Shoes', 'Performance running shoes with recycled and bio-based materials'),
        ('Bamboo Fiber Athletic Socks', 'Moisture-wicking socks with antibacterial bamboo fiber'),
        ('Natural Rubber Exercise Mat', 'Non-toxic exercise mat made from pure natural rubber'),
        ('Organic Hemp Jump Rope', 'Durable jump rope with hemp cord and wooden handles'),
        ('Recycled Plastic Sports Bag', 'Gym bag made from upcycled plastic bottles'),
        ('Solar-Powered Fitness Tracker', 'Activity tracker with solar charging capability'),
        ('Sustainable Protein Shaker', 'BPA-free shaker bottle made from plant-based materials')
    ],
    'Zero Waste Living': [
        ('Stainless Steel Straws Set', 'Reusable straws with cleaning brush and carrying case'),
        ('Beeswax Food Wraps', 'Natural alternative to plastic wrap made with organic cotton and beeswax'),
        ('Glass Food Storage Jars', 'Airtight storage jars made from recycled glass'),
        ('Bamboo Cutlery Set', 'Portable utensil set with bamboo fork, knife, spoon, and chopsticks'),
        ('Reusable Produce Bags', 'Mesh bags for plastic-free grocery shopping'),
        ('Compostable Trash Bags', 'Biodegradable bags that break down in home compost'),
        ('Silicone Food Storage Bags', 'Reusable bags replacing single-use plastic storage'),
        ('Bamboo Toothbrush Set', 'Biodegradable toothbrushes with soft natural bristles'),
        ('Stainless Steel Lunch Box', 'Durable lunch container with multiple compartments'),
        ('Natural Loofah Sponges', 'Biodegradable cleaning sponges grown from natural gourds'),
        ('Reusable Coffee Cup', 'Insulated travel cup made from sustainable bamboo fiber'),
        ('Organic Cotton Tote Bags', 'Sturdy shopping bags made from certified organic cotton'),
        ('Glass Water Bottle Set', 'Leak-proof bottles made from borosilicate glass'),
        ('Bamboo Soap Dispensers', 'Refillable dispensers with sustainable bamboo pumps'),
        ('Compostable Phone Case', 'Protective case that biodegrades at end of life')
    ],
    'Organic Food': [
        ('Organic Quinoa Grain', 'Premium quinoa grown without pesticides or synthetic fertilizers'),
        ('Fair Trade Coffee Beans', 'Ethically sourced coffee supporting sustainable farming communities'),
        ('Organic Coconut Oil', 'Cold-pressed virgin coconut oil from sustainable palm farms'),
        ('Raw Organic Honey', 'Unprocessed honey from local beekeepers practicing sustainable methods'),
        ('Organic Chia Seeds', 'Nutrient-rich seeds from certified organic farms'),
        ('Sustainable Sea Salt', 'Hand-harvested sea salt from pristine coastal waters'),
        ('Organic Almond Butter', 'Creamy nut butter made from California organic almonds'),
        ('Fair Trade Dark Chocolate', 'Rich chocolate supporting cocoa farmers and sustainable practices'),
        ('Organic Green Tea', 'Premium loose-leaf tea from sustainable tea gardens'),
        ('Local Organic Vegetables', 'Fresh seasonal vegetables from nearby organic farms'),
        ('Organic Olive Oil', 'Extra virgin olive oil from century-old sustainable groves'),
        ('Sustainable Protein Powder', 'Plant-based protein from organic pea and hemp sources'),
        ('Organic Dried Fruits', 'Sun-dried fruits without sulfites or artificial preservatives'),
        ('Fair Trade Spice Blend', 'Aromatic spices supporting small-scale farmers worldwide'),
        ('Organic Herbal Tea Set', 'Caffeine-free herbal teas from certified organic herbs')
    ],
    'Eco Baby & Kids': [
        ('Organic Cotton Baby Clothes', 'Soft baby clothing made from certified organic cotton'),
        ('Bamboo Baby Bottles', 'BPA-free bottles made from sustainable bamboo fiber'),
        ('Natural Rubber Teething Toys', 'Safe teething toys made from pure natural rubber'),
        ('Organic Baby Food Pouches', 'Nutritious baby food made from organic fruits and vegetables'),
        ('Eco-Friendly Diapers', 'Biodegradable diapers made from sustainable materials'),
        ('Wooden Educational Toys', 'Learning toys crafted from sustainably sourced wood'),
        ('Organic Baby Skincare Set', 'Gentle skincare products made with organic ingredients'),
        ('Bamboo High Chair', 'Adjustable high chair made from sustainable bamboo'),
        ('Natural Wool Baby Blanket', 'Cozy blanket made from ethically sourced organic wool'),
        ('Non-Toxic Crayons', 'Safe coloring crayons made from natural soy wax'),
        ('Organic Cotton Stuffed Animals', 'Cuddly toys made from organic cotton and natural filling'),
        ('Sustainable Baby Carrier', 'Ergonomic carrier made from organic cotton and hemp'),
        ('Wooden Building Blocks', 'Classic blocks made from FSC-certified sustainable wood'),
        ('Organic Baby Shampoo', 'Tear-free shampoo with organic chamomile and calendula'),
        ('Eco-Friendly Pacifiers', 'Natural rubber pacifiers free from harmful chemicals')
    ],
    'Green Garden': [
        ('Organic Compost Fertilizer', 'Nutrient-rich compost made from organic waste materials'),
        ('Bamboo Garden Tools Set', 'Durable gardening tools with sustainable bamboo handles'),
        ('Recycled Plastic Planters', 'Attractive planters made from recycled ocean plastic'),
        ('Organic Seed Starter Kit', 'Everything needed to start an organic vegetable garden'),
        ('Natural Pest Control Spray', 'Chemical-free pest control using organic essential oils'),
        ('Biodegradable Plant Pots', 'Seedling pots that decompose naturally in soil'),
        ('Organic Potting Soil Mix', 'Premium soil blend made from organic compost and materials'),
        ('Solar-Powered Garden Lights', 'Decorative lighting powered by renewable solar energy'),
        ('Rainwater Collection System', 'Sustainable water harvesting for garden irrigation'),
        ('Organic Mulch Blend', 'Natural mulch to retain moisture and suppress weeds'),
        ('Companion Planting Guide', 'Educational guide for sustainable gardening practices'),
        ('Natural Garden Markers', 'Plant labels made from sustainable bamboo and cork'),
        ('Organic Herb Garden Kit', 'Complete kit for growing culinary herbs organically'),
        ('Beneficial Insect House', 'Habitat to attract helpful insects for natural pest control'),
        ('Sustainable Watering Can', 'Galvanized steel watering can with ergonomic design')
    ],
    'Renewable Energy': [
        ('Residential Solar Panel Kit', 'Complete solar system for home energy independence'),
        ('Portable Solar Generator', 'Compact power station for camping and emergency backup'),
        ('Wind Turbine for Home', 'Small wind generator for residential renewable energy'),
        ('Solar Water Heater System', 'Efficient system for heating water using solar energy'),
        ('Energy Storage Battery', 'Lithium battery system for storing renewable energy'),
        ('Solar-Powered Attic Fan', 'Ventilation fan powered by integrated solar panel'),
        ('Micro Hydroelectric Generator', 'Small-scale water power generator for streams'),
        ('Solar Charge Controller', 'Smart controller for optimizing solar panel efficiency'),
        ('Renewable Energy Monitor', 'Device to track home energy production and consumption'),
        ('Solar Pool Heater', 'Eco-friendly pool heating using solar thermal technology'),
        ('Wind-Solar Hybrid System', 'Combined wind and solar power generation system'),
        ('Solar-Powered Greenhouse Kit', 'Complete greenhouse with integrated solar systems'),
        ('Energy-Efficient Heat Pump', 'High-efficiency heating and cooling system'),
        ('Solar Inverter System', 'Convert DC solar power to AC for home use'),
        ('Renewable Energy Consultation', 'Professional assessment for home energy solutions')
    ]
}

# Sample sustainable seller businesses
SUSTAINABLE_SELLERS = [
    {
        'business_name': 'EcoLiving Solutions',
        'contact_person': 'Sarah Green',
        'description': 'Specializing in sustainable home products and zero-waste living solutions',
        'sustainability_focus': 'Zero waste lifestyle products and eco-friendly home solutions'
    },
    {
        'business_name': 'Organic Threads Co.',
        'contact_person': 'Michael Earth',
        'description': 'Ethical fashion brand creating clothing from organic and recycled materials',
        'sustainability_focus': 'Sustainable fashion using organic cotton, hemp, and recycled materials'
    },
    {
        'business_name': 'Green Tech Innovations',
        'contact_person': 'Lisa Solar',
        'description': 'Developing energy-efficient electronics and renewable energy solutions',
        'sustainability_focus': 'Clean technology and energy-efficient electronic devices'
    },
    {
        'business_name': 'Pure Beauty Naturals',
        'contact_person': 'Emma Natural',
        'description': 'Cruelty-free beauty products made from organic and natural ingredients',
        'sustainability_focus': 'Organic skincare and cosmetics with zero animal testing'
    },
    {
        'business_name': 'Active Earth Sports',
        'contact_person': 'Jake Fitness',
        'description': 'Sustainable sports equipment made from recycled and eco-friendly materials',
        'sustainability_focus': 'Eco-friendly sports gear and sustainable athletic equipment'
    },
    {
        'business_name': 'Zero Waste Marketplace',
        'contact_person': 'Anna Plastic-Free',
        'description': 'Curated selection of products for plastic-free and zero-waste living',
        'sustainability_focus': 'Plastic-free alternatives and zero-waste lifestyle products'
    },
    {
        'business_name': 'Organic Harvest Foods',
        'contact_person': 'David Farm',
        'description': 'Locally sourced organic foods supporting sustainable agriculture',
        'sustainability_focus': 'Organic food production and sustainable farming practices'
    },
    {
        'business_name': 'Little Green Sprouts',
        'contact_person': 'Maria Baby',
        'description': 'Safe and sustainable products for babies and young children',
        'sustainability_focus': 'Non-toxic baby products and sustainable children\'s items'
    },
    {
        'business_name': 'Garden Earth Organics',
        'contact_person': 'Tom Grow',
        'description': 'Organic gardening supplies and sustainable growing solutions',
        'sustainability_focus': 'Organic gardening and sustainable agriculture supplies'
    },
    {
        'business_name': 'Solar Solutions Pro',
        'contact_person': 'Rachel Power',
        'description': 'Renewable energy systems and energy-efficient home solutions',
        'sustainability_focus': 'Solar power systems and renewable energy technology'
    }
]

# Sustainability hashtags for community posts
SUSTAINABILITY_HASHTAGS = [
    'sustainable', 'ecofriendly', 'zerowaste', 'organic', 'renewable',
    'climateaction', 'greenliving', 'ethicalfashion', 'plasticfree',
    'fairtrade', 'carbonneutral', 'biodegradable', 'upcycled',
    'consciousliving', 'sustainablefashion', 'greentech', 'solarpowered',
    'crueltyfree', 'locallysourced', 'energyefficient', 'recycled',
    'naturalbeauty', 'organicfood', 'sustainableliving', 'ecohome'
]

    def clear_existing_data(self):
        """Clear existing data from all tables"""
        print("🗑️  Clearing existing data...")

        try:
            # Clear in reverse dependency order to avoid foreign key constraints
            tables_to_clear = [
                'SearchConversion', 'SearchClick', 'ChatMessage', 'ChatSession',
                'DataExportRequest', 'CookieAuditLog', 'CookieConsentHistory',
                'CookieConsent', 'VisualSearchAnalytics', 'SearchAnalytics',
                'NewsletterSubscription', 'ContentPage', 'Banner', 'SupportAttachment',
                'SupportMessage', 'SupportTicket', 'ProductComparison',
                'AvailabilityNotification', 'RecentlyViewed', 'EmailNotification',
                'AbandonedCart', 'SavedCart', 'GuestSession', 'UserOAuth',
                'OAuthProvider', 'CommunityStats', 'PriceHistory', 'Sales',
                'UserSession', 'UserBehaviorProfile', 'UserInteractionLog',
                'Refund', 'Invoice', 'PaymentTransaction', 'PaymentGateway',
                'TaxRate', 'CouponUsage', 'Coupon', 'ShippingMethod', 'ShippingZone',
                'PaymentMethod', 'UserAddress', 'PostHashtag', 'Hashtag',
                'PostLike', 'PostComment', 'CommunityPost', 'Wishlist',
                'CartItem', 'ProductVariant', 'ProductImage', 'ProductReview',
                'OrderItem', 'Order', 'Product', 'Category', 'SellerPayout',
                'SellerCommission', 'SellerStore', 'Seller', 'AdminUser', 'User',
                'SyncQueue', 'InventoryConflict', 'InventorySyncLog', 'ChannelInventory',
                'SalesChannel', 'InventoryLog', 'RMAStats', 'RMAConfiguration',
                'RMARule', 'RMAApproval', 'ReturnShipment', 'RMADocument',
                'RMATimeline', 'RMAItem', 'RMARequest', 'CarrierRate',
                'FulfillmentRule', 'TrackingEvent', 'Shipment', 'ShippingCarrier'
            ]

            for table_name in tables_to_clear:
                if table_name in self.models:
                    try:
                        self.models[table_name].query.delete()
                        print(f"   Cleared {table_name}")
                    except Exception as e:
                        print(f"   Warning: Could not clear {table_name}: {e}")

            self.db.session.commit()
            print("✅ Successfully cleared existing data")

        except Exception as e:
            print(f"❌ Error clearing data: {e}")
            self.db.session.rollback()
            raise

    def create_categories(self):
        """Create product categories"""
        print("📂 Creating product categories...")

        for category_name, category_info in SUSTAINABLE_CATEGORIES.items():
            category = self.models['Category'](
                name=category_name,
                slug=category_name.lower().replace(' ', '-').replace('&', 'and'),
                description=category_info['description'],
                is_active=True,
                sort_order=len(self.created_data['categories']) + 1,
                meta_title=f"{category_name} - Sustainable Products",
                meta_description=category_info['description'][:160],
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )

            self.db.session.add(category)
            self.created_data['categories'].append(category)

        self.db.session.commit()
        print(f"✅ Created {len(self.created_data['categories'])} categories")

    def create_admin_users(self):
        """Create admin users"""
        print("👑 Creating admin users...")

        admin_users_data = [
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'password': 'admin123',
                'first_name': 'System',
                'last_name': 'Administrator',
                'role': 'super_admin',
                'is_active': True,
                'is_super_admin': True
            },
            {
                'username': 'sustainability_manager',
                'email': '<EMAIL>',
                'password': 'sustain123',
                'first_name': 'Green',
                'last_name': 'Manager',
                'role': 'admin',
                'is_active': True,
                'is_super_admin': False
            },
            {
                'username': 'product_curator',
                'email': '<EMAIL>',
                'password': 'curator123',
                'first_name': 'Eco',
                'last_name': 'Curator',
                'role': 'moderator',
                'is_active': True,
                'is_super_admin': False
            }
        ]

        for admin_data in admin_users_data:
            admin_user = self.models['AdminUser'](
                username=admin_data['username'],
                email=admin_data['email'],
                password=hash_password(admin_data['password']),
                first_name=admin_data['first_name'],
                last_name=admin_data['last_name'],
                role=admin_data['role'],
                is_active=admin_data['is_active'],
                is_super_admin=admin_data['is_super_admin'],
                created_at=datetime.utcnow()
            )

            self.db.session.add(admin_user)
            self.created_data['admin_users'].append(admin_user)

        self.db.session.commit()
        print(f"✅ Created {len(self.created_data['admin_users'])} admin users")

    def create_users(self):
        """Create regular users"""
        print("👥 Creating users...")

        # Create 50 diverse users interested in sustainability
        sustainability_interests = [
            'zero waste living', 'organic food', 'renewable energy', 'ethical fashion',
            'eco-friendly home', 'natural beauty', 'sustainable gardening', 'green technology',
            'fair trade products', 'plastic-free lifestyle', 'carbon neutral living',
            'sustainable transportation', 'organic farming', 'renewable materials'
        ]

        for i in range(50):
            user = self.models['User'](
                username=fake.user_name() + str(random.randint(100, 999)),
                email=fake.email(),
                password=hash_password('password123'),
                first_name=fake.first_name(),
                last_name=fake.last_name(),
                phone=fake.phone_number()[:15],
                date_of_birth=fake.date_of_birth(minimum_age=18, maximum_age=70),
                gender=random.choice(['male', 'female', 'other', 'prefer_not_to_say']),
                is_active=True,
                email_verified=random.choice([True, False]),
                phone_verified=random.choice([True, False]),
                newsletter_subscribed=random.choice([True, False]),
                marketing_emails=random.choice([True, False]),
                sustainability_score=random.randint(60, 100),
                preferred_categories=json.dumps(random.sample(list(SUSTAINABLE_CATEGORIES.keys()),
                                                            random.randint(2, 5))),
                bio=f"Passionate about {random.choice(sustainability_interests)} and sustainable living.",
                created_at=random_datetime_between(
                    datetime.utcnow() - timedelta(days=365),
                    datetime.utcnow() - timedelta(days=30)
                ),
                last_login=random_datetime_between(
                    datetime.utcnow() - timedelta(days=30),
                    datetime.utcnow()
                )
            )

            self.db.session.add(user)
            self.created_data['users'].append(user)

        self.db.session.commit()
        print(f"✅ Created {len(self.created_data['users'])} users")

    def create_sellers(self):
        """Create seller accounts"""
        print("🏪 Creating sellers...")

        for seller_data in SUSTAINABLE_SELLERS:
            seller = self.models['Seller'](
                business_name=seller_data['business_name'],
                contact_person=seller_data['contact_person'],
                email=fake.company_email(),
                phone=fake.phone_number()[:15],
                business_type='sustainable_products',
                business_registration_number=fake.bothify(text='REG-####-####'),
                tax_id=fake.bothify(text='TAX-########'),
                description=seller_data['description'],
                website_url=f"https://www.{seller_data['business_name'].lower().replace(' ', '')}.com",
                business_address=fake.address()[:200],
                city=fake.city(),
                state=fake.state(),
                postal_code=fake.postcode(),
                country='India',
                bank_account_number=fake.bothify(text='################'),
                bank_name=fake.company(),
                bank_routing_number=fake.bothify(text='########'),
                commission_rate=random.uniform(5.0, 15.0),
                is_verified=True,
                is_active=True,
                verification_status='approved',
                sustainability_certification=True,
                sustainability_score=random.randint(80, 100),
                total_products=0,
                total_orders=0,
                total_revenue=0.0,
                total_earnings=0.0,
                created_at=random_datetime_between(
                    datetime.utcnow() - timedelta(days=300),
                    datetime.utcnow() - timedelta(days=60)
                )
            )

            self.db.session.add(seller)
            self.created_data['sellers'].append(seller)

        self.db.session.commit()
        print(f"✅ Created {len(self.created_data['sellers'])} sellers")

    def create_products(self):
        """Create sustainable products"""
        print("🌱 Creating sustainable products...")

        product_count = 0
        target_products = 120  # Create 120 products total

        for category_name, products_list in SUSTAINABLE_PRODUCTS.items():
            category = next((c for c in self.created_data['categories'] if c.name == category_name), None)
            if not category:
                continue

            category_info = SUSTAINABLE_CATEGORIES[category_name]

            # Create products for this category
            for product_name, product_description in products_list:
                if product_count >= target_products:
                    break

                # Select random seller
                seller = random.choice(self.created_data['sellers'])

                # Generate price within category range
                min_price, max_price = category_info['price_range']
                base_price = random.uniform(min_price, max_price)

                # Add some price variation
                price = round(base_price * random.uniform(0.8, 1.2), 2)

                # Generate sustainability score
                min_score, max_score = category_info['sustainability_score']
                sustainability_score = random.randint(min_score, max_score)

                # Create detailed product description
                keywords = category_info['keywords']
                detailed_description = f"{product_description}. "
                detailed_description += f"This product features {random.choice(keywords)} materials and supports sustainable practices. "
                detailed_description += f"Perfect for eco-conscious consumers looking for {random.choice(keywords)} alternatives. "
                detailed_description += f"Sustainability score: {sustainability_score}/10."

                # Generate stock quantity
                stock_quantity = random.randint(10, 200)

                product = self.models['Product'](
                    name=product_name,
                    description=detailed_description,
                    short_description=product_description,
                    price=price,
                    compare_price=round(price * random.uniform(1.1, 1.3), 2),
                    cost_price=round(price * random.uniform(0.4, 0.7), 2),
                    sku=f"ECO-{fake.bothify(text='####-####')}",
                    barcode=fake.ean13(),
                    stock_quantity=stock_quantity,
                    low_stock_threshold=random.randint(5, 20),
                    category_id=category.id,
                    seller_id=seller.id,
                    brand=seller.business_name,
                    weight=random.uniform(0.1, 5.0),
                    dimensions=json.dumps({
                        'length': random.uniform(5, 50),
                        'width': random.uniform(5, 50),
                        'height': random.uniform(2, 30)
                    }),
                    is_active=True,
                    is_featured=random.choice([True, False]),
                    is_digital=False,
                    requires_shipping=True,
                    sustainability_score=sustainability_score,
                    carbon_footprint=random.uniform(0.1, 5.0),
                    recyclable=random.choice([True, False]),
                    biodegradable=random.choice([True, False]),
                    eco_friendly_packaging=True,
                    fair_trade_certified=random.choice([True, False]),
                    organic_certified=random.choice([True, False]),
                    local_sourced=random.choice([True, False]),
                    meta_title=f"{product_name} - Sustainable {category_name}",
                    meta_description=product_description[:160],
                    tags=json.dumps(random.sample(keywords, min(3, len(keywords)))),
                    created_at=random_datetime_between(
                        datetime.utcnow() - timedelta(days=180),
                        datetime.utcnow() - timedelta(days=1)
                    ),
                    updated_at=datetime.utcnow()
                )

                self.db.session.add(product)
                self.created_data['products'].append(product)
                product_count += 1

                # Update seller product count
                seller.total_products += 1

        self.db.session.commit()
        print(f"✅ Created {len(self.created_data['products'])} sustainable products")

    def create_product_images(self):
        """Create product images"""
        print("📸 Creating product images...")

        # Sample sustainable product image URLs (placeholder URLs)
        image_base_urls = [
            "https://images.unsplash.com/photo-1542601906990-b4d3fb778b09",  # Eco products
            "https://images.unsplash.com/photo-1558618666-fcd25c85cd64",  # Sustainable items
            "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b",  # Green products
            "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136",  # Organic items
            "https://images.unsplash.com/photo-1584464491033-06628f3a6b7b",  # Natural products
            "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136",  # Bamboo products
            "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b",  # Eco-friendly
            "https://images.unsplash.com/photo-1542601906990-b4d3fb778b09"   # Sustainable
        ]

        for product in self.created_data['products']:
            # Create 2-4 images per product
            num_images = random.randint(2, 4)

            for i in range(num_images):
                image_url = f"{random.choice(image_base_urls)}?w=800&h=600&fit=crop&crop=center&q=80&auto=format"

                product_image = self.models['ProductImage'](
                    product_id=product.id,
                    image_url=image_url,
                    alt_text=f"{product.name} - Image {i+1}",
                    is_primary=(i == 0),
                    sort_order=i + 1,
                    created_at=product.created_at
                )

                self.db.session.add(product_image)

        self.db.session.commit()
        print("✅ Created product images")

    def create_orders(self):
        """Create realistic order data"""
        print("🛒 Creating orders...")

        order_statuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled']
        payment_statuses = ['pending', 'paid', 'failed', 'refunded']
        payment_methods = ['credit_card', 'debit_card', 'upi', 'net_banking', 'wallet', 'cod']

        # Create 200 orders
        for i in range(200):
            user = random.choice(self.created_data['users'])

            # Generate order date
            order_date = random_datetime_between(
                datetime.utcnow() - timedelta(days=120),
                datetime.utcnow() - timedelta(days=1)
            )

            # Select 1-5 products for this order
            order_products = random.sample(self.created_data['products'], random.randint(1, 5))

            # Calculate order totals
            subtotal = 0
            total_items = 0

            for product in order_products:
                quantity = random.randint(1, 3)
                subtotal += product.price * quantity
                total_items += quantity

            # Add shipping and tax
            shipping_cost = random.uniform(5, 25) if subtotal < 500 else 0  # Free shipping over 500
            tax_amount = subtotal * 0.18  # 18% GST
            total_amount = subtotal + shipping_cost + tax_amount

            order = self.models['Order'](
                user_id=user.id,
                order_number=f"ORD-{fake.bothify(text='####-####')}",
                status=random.choice(order_statuses),
                payment_status=random.choice(payment_statuses),
                payment_method=random.choice(payment_methods),
                subtotal=round(subtotal, 2),
                tax_amount=round(tax_amount, 2),
                shipping_cost=round(shipping_cost, 2),
                discount_amount=0.0,
                total_amount=round(total_amount, 2),
                currency='INR',
                total_items=total_items,
                shipping_address=json.dumps({
                    'name': f"{user.first_name} {user.last_name}",
                    'address_line_1': fake.street_address(),
                    'address_line_2': fake.secondary_address(),
                    'city': fake.city(),
                    'state': fake.state(),
                    'postal_code': fake.postcode(),
                    'country': 'India',
                    'phone': user.phone
                }),
                billing_address=json.dumps({
                    'name': f"{user.first_name} {user.last_name}",
                    'address_line_1': fake.street_address(),
                    'city': fake.city(),
                    'state': fake.state(),
                    'postal_code': fake.postcode(),
                    'country': 'India'
                }),
                tracking_number=f"TRK-{fake.bothify(text='############')}" if random.choice([True, False]) else None,
                estimated_delivery=order_date + timedelta(days=random.randint(3, 10)),
                created_at=order_date,
                updated_at=order_date
            )

            self.db.session.add(order)
            self.created_data['orders'].append(order)

            # Create order items
            for product in order_products:
                quantity = random.randint(1, 3)
                unit_price = product.price

                order_item = self.models['OrderItem'](
                    order_id=order.id,
                    product_id=product.id,
                    quantity=quantity,
                    unit_price=unit_price,
                    total_price=round(unit_price * quantity, 2),
                    product_name=product.name,
                    product_sku=product.sku,
                    created_at=order_date
                )

                self.db.session.add(order_item)

                # Update product stock
                product.stock_quantity = max(0, product.stock_quantity - quantity)

                # Update seller stats
                seller = next((s for s in self.created_data['sellers'] if s.id == product.seller_id), None)
                if seller:
                    seller.total_orders += 1
                    seller.total_revenue += unit_price * quantity
                    seller.total_earnings += unit_price * quantity * (seller.commission_rate / 100)

        self.db.session.commit()
        print(f"✅ Created {len(self.created_data['orders'])} orders with items")

    def create_product_reviews(self):
        """Create product reviews"""
        print("⭐ Creating product reviews...")

        review_templates = [
            "Amazing sustainable product! Really happy with the quality and eco-friendly packaging.",
            "Love this {product_name}! It's exactly what I was looking for in terms of sustainability.",
            "Great quality and truly sustainable. Would definitely recommend to other eco-conscious buyers.",
            "Perfect for my zero-waste lifestyle. The {product_name} exceeded my expectations.",
            "Excellent sustainable alternative. The quality is outstanding and it's environmentally friendly.",
            "Really impressed with this eco-friendly product. Great value for money.",
            "This {product_name} is exactly what sustainable living needs. Highly recommended!",
            "Outstanding quality and truly sustainable. Will definitely buy again.",
            "Perfect sustainable product. Great for anyone trying to live more eco-friendly.",
            "Excellent choice for environmentally conscious consumers. Very satisfied!"
        ]

        # Create 500+ reviews
        for _ in range(500):
            product = random.choice(self.created_data['products'])
            user = random.choice(self.created_data['users'])

            # Check if user already reviewed this product
            existing_review = any(
                r.product_id == product.id and r.user_id == user.id
                for r in self.created_data['reviews']
            )

            if existing_review:
                continue

            rating = random.choices([1, 2, 3, 4, 5], weights=[2, 3, 10, 35, 50])[0]  # Weighted towards higher ratings

            review_text = random.choice(review_templates).format(product_name=product.name.lower())

            # Add specific sustainability comments for high ratings
            if rating >= 4:
                sustainability_comments = [
                    " The sustainable packaging was a nice touch.",
                    " Love that it's made from recycled materials.",
                    " Great to support eco-friendly businesses.",
                    " Perfect for my sustainable lifestyle.",
                    " The carbon footprint information was helpful."
                ]
                review_text += random.choice(sustainability_comments)

            review_date = random_datetime_between(
                product.created_at + timedelta(days=1),
                datetime.utcnow()
            )

            review = self.models['ProductReview'](
                product_id=product.id,
                user_id=user.id,
                rating=rating,
                title=f"{'Great' if rating >= 4 else 'Good' if rating >= 3 else 'Okay'} sustainable product",
                review_text=review_text,
                is_verified_purchase=random.choice([True, False]),
                is_approved=True,
                helpful_count=random.randint(0, 20),
                created_at=review_date,
                updated_at=review_date
            )

            self.db.session.add(review)
            self.created_data['reviews'].append(review)

        self.db.session.commit()
        print(f"✅ Created {len(self.created_data['reviews'])} product reviews")

    def create_hashtags(self):
        """Create sustainability hashtags"""
        print("🏷️  Creating hashtags...")

        for tag in SUSTAINABILITY_HASHTAGS:
            hashtag = self.models['Hashtag'](
                tag=tag,
                usage_count=random.randint(5, 100),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )

            self.db.session.add(hashtag)
            self.created_data['hashtags'].append(hashtag)

        self.db.session.commit()
        print(f"✅ Created {len(self.created_data['hashtags'])} hashtags")

    def create_community_posts(self):
        """Create community posts about sustainability"""
        print("💬 Creating community posts...")

        post_templates = [
            "Just switched to {product_type} and loving the sustainable impact! 🌱",
            "Amazing how much waste I've reduced with these eco-friendly alternatives! ♻️",
            "Sharing my latest sustainable find - {product_type}. Highly recommend! 🌍",
            "One year into my zero-waste journey and these products have been game-changers! 🌿",
            "Love supporting businesses that prioritize sustainability! 💚",
            "Small changes, big impact! These {product_type} are perfect for eco-living. 🌱",
            "Sustainable living tip: Try {product_type} for a greener lifestyle! 🌍",
            "Excited to share my latest eco-friendly purchase! {product_type} 🌿",
            "Making the switch to sustainable products one item at a time! ♻️",
            "These {product_type} prove that sustainable can be stylish too! 💚"
        ]

        product_types = [
            'bamboo products', 'organic cotton items', 'recycled materials',
            'solar-powered gadgets', 'zero-waste essentials', 'natural beauty products',
            'sustainable fashion', 'eco-friendly home goods', 'organic food items',
            'renewable energy solutions'
        ]

        # Create 150 community posts
        for _ in range(150):
            user = random.choice(self.created_data['users'])
            product_type = random.choice(product_types)

            content = random.choice(post_templates).format(product_type=product_type)

            # Add more detailed content for some posts
            if random.choice([True, False]):
                additional_content = [
                    " The quality is amazing and I love knowing I'm making a positive environmental impact.",
                    " It's incredible how these small changes can make such a big difference for our planet.",
                    " Perfect for anyone looking to live more sustainably without compromising on quality.",
                    " The packaging was completely plastic-free which was a nice surprise!",
                    " Supporting companies that care about the environment feels so good."
                ]
                content += random.choice(additional_content)

            post_date = random_datetime_between(
                datetime.utcnow() - timedelta(days=90),
                datetime.utcnow()
            )

            post = self.models['CommunityPost'](
                user_id=user.id,
                content=content,
                post_type=random.choice(['text', 'photo', 'review']),
                image_url=f"https://images.unsplash.com/photo-{random.randint(1500000000000, 1600000000000)}" if random.choice([True, False]) else None,
                likes_count=random.randint(0, 50),
                comments_count=random.randint(0, 15),
                shares_count=random.randint(0, 10),
                created_at=post_date,
                updated_at=post_date
            )

            self.db.session.add(post)
            self.created_data['posts'].append(post)

            # Add hashtags to posts
            post_hashtags = random.sample(self.created_data['hashtags'], random.randint(1, 4))
            for hashtag in post_hashtags:
                post_hashtag = self.models['PostHashtag'](
                    post_id=post.id,
                    hashtag_id=hashtag.id,
                    created_at=post_date
                )
                self.db.session.add(post_hashtag)

                # Update hashtag usage count
                hashtag.usage_count += 1

        self.db.session.commit()
        print(f"✅ Created {len(self.created_data['posts'])} community posts")

    def create_community_engagement(self):
        """Create likes and comments for community posts"""
        print("❤️  Creating community engagement...")

        comment_templates = [
            "This is amazing! Thanks for sharing! 🌱",
            "I've been looking for something like this! Where did you get it?",
            "Love this! Sustainability is so important 💚",
            "Great choice! I have the same one and love it!",
            "This is exactly what I need for my eco-friendly lifestyle!",
            "Thanks for the recommendation! Adding to my wishlist 🌿",
            "Sustainable living goals! 🌍",
            "This looks perfect! How long have you been using it?",
            "Love seeing more people make sustainable choices! ♻️",
            "Great post! More people need to see this!"
        ]

        # Create likes for posts
        for post in self.created_data['posts']:
            # Random number of likes (already set in post.likes_count)
            num_likes = min(post.likes_count, len(self.created_data['users']))
            liked_users = random.sample(self.created_data['users'], num_likes)

            for user in liked_users:
                like = self.models['PostLike'](
                    post_id=post.id,
                    user_id=user.id,
                    created_at=random_datetime_between(
                        post.created_at,
                        post.created_at + timedelta(days=7)
                    )
                )
                self.db.session.add(like)

        # Create comments for posts
        for post in self.created_data['posts']:
            # Random number of comments (already set in post.comments_count)
            for _ in range(post.comments_count):
                user = random.choice(self.created_data['users'])

                comment = self.models['PostComment'](
                    post_id=post.id,
                    user_id=user.id,
                    content=random.choice(comment_templates),
                    created_at=random_datetime_between(
                        post.created_at,
                        post.created_at + timedelta(days=10)
                    ),
                    updated_at=datetime.utcnow()
                )
                self.db.session.add(comment)

        self.db.session.commit()
        print("✅ Created community engagement (likes and comments)")

    def create_supporting_data(self):
        """Create supporting data like shipping zones, payment gateways, etc."""
        print("🔧 Creating supporting data...")

        # Create shipping zones
        shipping_zones_data = [
            {
                'name': 'India Domestic',
                'countries': ['IN'],
                'states': None,
                'postal_codes': None,
                'is_active': True
            },
            {
                'name': 'International',
                'countries': ['US', 'UK', 'CA', 'AU', 'DE', 'FR'],
                'states': None,
                'postal_codes': None,
                'is_active': True
            }
        ]

        for zone_data in shipping_zones_data:
            zone = self.models['ShippingZone'](
                name=zone_data['name'],
                countries=json.dumps(zone_data['countries']),
                states=json.dumps(zone_data['states']) if zone_data['states'] else None,
                postal_codes=json.dumps(zone_data['postal_codes']) if zone_data['postal_codes'] else None,
                is_active=zone_data['is_active']
            )
            self.db.session.add(zone)
            self.created_data['shipping_zones'].append(zone)

        # Create shipping methods
        shipping_methods_data = [
            {'name': 'Standard Shipping', 'description': 'Eco-friendly standard delivery', 'base_cost': 50.0, 'per_kg_cost': 10.0, 'min_delivery_days': 3, 'max_delivery_days': 7},
            {'name': 'Express Shipping', 'description': 'Fast delivery with carbon offset', 'base_cost': 100.0, 'per_kg_cost': 20.0, 'min_delivery_days': 1, 'max_delivery_days': 3},
            {'name': 'Free Shipping', 'description': 'Free sustainable shipping over ₹500', 'base_cost': 0.0, 'per_kg_cost': 0.0, 'min_delivery_days': 5, 'max_delivery_days': 10}
        ]

        for method_data in shipping_methods_data:
            for zone in self.created_data['shipping_zones']:
                method = self.models['ShippingMethod'](
                    zone_id=zone.id,
                    name=method_data['name'],
                    description=method_data['description'],
                    base_cost=method_data['base_cost'],
                    per_kg_cost=method_data['per_kg_cost'],
                    free_shipping_threshold=500.0 if method_data['name'] == 'Free Shipping' else None,
                    min_delivery_days=method_data['min_delivery_days'],
                    max_delivery_days=method_data['max_delivery_days'],
                    is_active=True
                )
                self.db.session.add(method)
                self.created_data['shipping_methods'].append(method)

        # Create coupons
        coupons_data = [
            {'code': 'SUSTAINABLE20', 'name': 'Sustainable Living 20% Off', 'discount_type': 'percentage', 'discount_value': 20.0, 'minimum_amount': 200.0},
            {'code': 'ECOFRIENDLY15', 'name': 'Eco-Friendly 15% Discount', 'discount_type': 'percentage', 'discount_value': 15.0, 'minimum_amount': 150.0},
            {'code': 'GREENLIVING50', 'name': 'Green Living ₹50 Off', 'discount_type': 'fixed', 'discount_value': 50.0, 'minimum_amount': 300.0},
            {'code': 'ZEROWASTE25', 'name': 'Zero Waste 25% Off', 'discount_type': 'percentage', 'discount_value': 25.0, 'minimum_amount': 500.0},
            {'code': 'NEWUSER10', 'name': 'New User 10% Off', 'discount_type': 'percentage', 'discount_value': 10.0, 'minimum_amount': 100.0}
        ]

        for coupon_data in coupons_data:
            coupon = self.models['Coupon'](
                code=coupon_data['code'],
                name=coupon_data['name'],
                description=f"Get {coupon_data['discount_value']}{'%' if coupon_data['discount_type'] == 'percentage' else '₹'} off on sustainable products",
                discount_type=coupon_data['discount_type'],
                discount_value=coupon_data['discount_value'],
                minimum_amount=coupon_data['minimum_amount'],
                maximum_discount=100.0 if coupon_data['discount_type'] == 'percentage' else None,
                usage_limit=1000,
                usage_limit_per_user=5,
                is_active=True,
                valid_from=datetime.utcnow() - timedelta(days=30),
                valid_until=datetime.utcnow() + timedelta(days=90),
                created_at=datetime.utcnow()
            )
            self.db.session.add(coupon)
            self.created_data['coupons'].append(coupon)

        # Create tax rates
        tax_rate = self.models['TaxRate'](
            name='GST India',
            rate=0.18,  # 18% GST
            country='India',
            state=None,
            is_active=True,
            created_at=datetime.utcnow()
        )
        self.db.session.add(tax_rate)
        self.created_data['tax_rates'].append(tax_rate)

        # Create payment gateways
        payment_gateways_data = [
            {'name': 'razorpay', 'display_name': 'Razorpay', 'is_active': True, 'is_default': True},
            {'name': 'stripe', 'display_name': 'Stripe', 'is_active': True, 'is_default': False},
            {'name': 'paypal', 'display_name': 'PayPal', 'is_active': True, 'is_default': False}
        ]

        for gateway_data in payment_gateways_data:
            gateway = self.models['PaymentGateway'](
                name=gateway_data['name'],
                display_name=gateway_data['display_name'],
                is_active=gateway_data['is_active'],
                is_default=gateway_data['is_default'],
                supported_currencies=json.dumps(['INR', 'USD']),
                transaction_fee_percentage=2.5,
                transaction_fee_fixed=2.0,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            self.db.session.add(gateway)
            self.created_data['payment_gateways'].append(gateway)

        # Create OAuth providers
        oauth_providers_data = [
            {'name': 'google', 'display_name': 'Google', 'client_id': 'google-client-id', 'client_secret': 'google-client-secret'},
            {'name': 'facebook', 'display_name': 'Facebook', 'client_id': 'facebook-client-id', 'client_secret': 'facebook-client-secret'}
        ]

        for provider_data in oauth_providers_data:
            provider = self.models['OAuthProvider'](
                name=provider_data['name'],
                display_name=provider_data['display_name'],
                client_id=provider_data['client_id'],
                client_secret=provider_data['client_secret'],
                is_active=True,
                created_at=datetime.utcnow()
            )
            self.db.session.add(provider)
            self.created_data['oauth_providers'].append(provider)

        # Create shipping carriers
        carriers_data = [
            {'name': 'Shiprocket', 'code': 'SHIPROCKET', 'is_active': True, 'is_default': True},
            {'name': 'Delhivery', 'code': 'DELHIVERY', 'is_active': True, 'is_default': False},
            {'name': 'Ekart', 'code': 'EKART', 'is_active': True, 'is_default': False}
        ]

        for carrier_data in carriers_data:
            carrier = self.models['ShippingCarrier'](
                name=carrier_data['name'],
                code=carrier_data['code'],
                is_active=carrier_data['is_active'],
                is_default=carrier_data['is_default'],
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            self.db.session.add(carrier)
            self.created_data['shipping_carriers'].append(carrier)

        self.db.session.commit()
        print("✅ Created supporting data (shipping, payments, etc.)")

    def run_seeding(self):
        """Run the complete database seeding process"""
        print("🌱 Starting Allora Sustainable E-commerce Database Seeding")
        print("=" * 60)

        try:
            # Initialize models
            if not self.initialize_models():
                print("❌ Failed to initialize models. Exiting.")
                return False

            # Clear existing data
            self.clear_existing_data()

            # Create data in dependency order
            self.create_categories()
            self.create_admin_users()
            self.create_users()
            self.create_sellers()
            self.create_products()
            self.create_product_images()
            self.create_orders()
            self.create_product_reviews()
            self.create_hashtags()
            self.create_community_posts()
            self.create_community_engagement()
            self.create_supporting_data()

            print("\n" + "=" * 60)
            print("🎉 Database seeding completed successfully!")
            print(f"📊 Created:")
            print(f"   • {len(self.created_data['categories'])} Categories")
            print(f"   • {len(self.created_data['admin_users'])} Admin Users")
            print(f"   • {len(self.created_data['users'])} Users")
            print(f"   • {len(self.created_data['sellers'])} Sellers")
            print(f"   • {len(self.created_data['products'])} Products")
            print(f"   • {len(self.created_data['orders'])} Orders")
            print(f"   • {len(self.created_data['reviews'])} Reviews")
            print(f"   • {len(self.created_data['posts'])} Community Posts")
            print(f"   • {len(self.created_data['hashtags'])} Hashtags")
            print(f"   • {len(self.created_data['coupons'])} Coupons")
            print(f"   • Supporting data (shipping, payments, etc.)")
            print("\n🌍 Your sustainable e-commerce platform is ready!")

            return True

        except Exception as e:
            print(f"\n❌ Error during seeding: {e}")
            self.db.session.rollback()
            return False

def main():
    """Main function to run the seeding script"""
    try:
        # Import Flask app and create application context
        from app import app

        with app.app_context():
            seeder = DatabaseSeeder()
            success = seeder.run_seeding()

            if success:
                print("\n✅ Seeding completed successfully!")
                return 0
            else:
                print("\n❌ Seeding failed!")
                return 1

    except Exception as e:
        print(f"❌ Failed to run seeding script: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
