# Allora Sustainable E-commerce Database Seeding

This directory contains comprehensive database seeding scripts for the Allora sustainable e-commerce platform. The seeding creates realistic data that reflects a thriving sustainable marketplace.

## 🌱 What Gets Seeded

### Core Data
- **120+ Sustainable Products** across 10 eco-friendly categories
- **50+ Users** with realistic profiles and sustainability preferences
- **10+ Sustainable Sellers** with authentic business profiles
- **200+ Orders** with realistic purchase patterns
- **500+ Product Reviews** with sustainability-focused feedback

### Categories
1. **Eco-Friendly Home** - Sustainable home products (bamboo, recycled materials)
2. **Organic Fashion** - Ethical clothing from organic materials
3. **Green Electronics** - Energy-efficient and eco-friendly devices
4. **Natural Beauty** - Organic and cruelty-free beauty products
5. **Sustainable Sports** - Eco-friendly sports equipment
6. **Zero Waste Living** - Products for plastic-free lifestyle
7. **Organic Food** - Locally sourced and organic food products
8. **Eco Baby & Kids** - Safe and sustainable children's products
9. **Green Garden** - Sustainable gardening supplies
10. **Renewable Energy** - Solar panels and clean energy solutions

### Community Data
- **150+ Community Posts** about sustainable living
- **25+ Hashtags** for sustainability topics
- **Likes and Comments** on community posts
- **User Engagement** data

### Supporting Data
- **Shipping Zones** (India Domestic, International)
- **Shipping Methods** (Standard, Express, Free shipping)
- **Payment Gateways** (Razorpay, Stripe, PayPal)
- **Coupons** for sustainable products
- **Tax Rates** (GST configuration)
- **OAuth Providers** (Google, Facebook)
- **Shipping Carriers** (Shiprocket, Delhivery, Ekart)

## 🚀 How to Run

### Prerequisites
1. **MySQL Database** running and accessible
2. **Python packages** installed (Flask, SQLAlchemy, Faker, etc.)
3. **Environment variables** configured in `.env`
4. **Database** `allora_db` created

### Quick Start
```bash
# Navigate to backend directory
cd allora/backend

# Run the seeding script
python run_seed.py
```

### Alternative Method
```bash
# Direct execution
python seed_database.py
```

### Manual Flask Context
```python
# If you need to run in Python shell
from app import app
with app.app_context():
    from seed_database import DatabaseSeeder
    seeder = DatabaseSeeder()
    seeder.run_seeding()
```

## 📊 Seeding Details

### Product Data
- **Realistic Names**: Bamboo Cutting Board Set, Solar Power Bank, Organic Cotton T-Shirt
- **Sustainability Scores**: 6-10 rating based on eco-friendliness
- **Detailed Descriptions**: Focus on sustainable materials and practices
- **Proper Pricing**: Category-appropriate price ranges
- **Stock Management**: Realistic inventory levels
- **Product Images**: Placeholder URLs for sustainable product photos

### User Profiles
- **Diverse Demographics**: Age range 18-70, various genders
- **Sustainability Focus**: Users interested in eco-friendly living
- **Realistic Data**: Generated using Faker library
- **Engagement History**: Purchase patterns and community participation

### Seller Businesses
- **Authentic Names**: EcoLiving Solutions, Organic Threads Co., Green Tech Innovations
- **Sustainability Focus**: Each seller specializes in specific eco-friendly categories
- **Business Details**: Complete profiles with contact information and certifications
- **Performance Metrics**: Sales data and commission tracking

### Order Patterns
- **Realistic Timing**: Orders spread over 120 days
- **Varied Quantities**: 1-5 products per order
- **Multiple Statuses**: Pending, confirmed, shipped, delivered
- **Payment Methods**: Credit card, UPI, net banking, COD
- **Shipping Addresses**: Complete Indian addresses

## 🔧 Configuration

### Environment Variables
Ensure these are set in your `.env` file:
```env
DATABASE_URL=mysql+mysqlconnector://root:password@localhost:3306/allora_db
FLASK_ENV=development
SECRET_KEY=your-secret-key
```

### Database Requirements
- MySQL 5.7+ or 8.0+
- Database `allora_db` must exist
- User with full permissions on the database

## 🛠️ Troubleshooting

### Common Issues

**Database Connection Error**
```
Solution: Check MySQL is running and credentials in .env are correct
```

**Import Errors**
```
Solution: Ensure all required packages are installed
pip install flask sqlalchemy faker mysql-connector-python
```

**Foreign Key Constraints**
```
Solution: The script handles dependencies automatically, but ensure database is empty before seeding
```

**Memory Issues**
```
Solution: The script commits data in batches to handle large datasets efficiently
```

### Clearing Data
The script automatically clears existing data before seeding. To manually clear:
```python
from app import app, db
with app.app_context():
    db.drop_all()
    db.create_all()
```

## 📈 Performance

- **Seeding Time**: Approximately 2-5 minutes depending on system
- **Database Size**: ~50MB after complete seeding
- **Memory Usage**: ~200MB during seeding process
- **Batch Processing**: Data committed in logical batches for efficiency

## 🌍 Sustainability Focus

All seeded data reflects authentic sustainable e-commerce practices:
- **Eco-friendly Products**: Real sustainable product categories and descriptions
- **Environmental Impact**: Carbon footprint and recyclability data
- **Ethical Business**: Fair trade and organic certifications
- **Community Engagement**: Sustainability-focused user discussions
- **Green Shipping**: Eco-friendly delivery options

## 📝 Data Quality

- **Realistic Relationships**: Proper foreign key relationships
- **Consistent Timestamps**: Logical creation and update dates
- **Varied Data**: Diverse product names, user profiles, and content
- **Business Logic**: Proper stock management and order processing
- **Sustainability Metrics**: Authentic environmental impact scores

## 🔄 Re-seeding

To re-seed the database:
1. The script automatically clears existing data
2. Run `python run_seed.py` again
3. All data will be regenerated with new random values

## 📞 Support

If you encounter issues with seeding:
1. Check the console output for specific error messages
2. Verify database connection and permissions
3. Ensure all required packages are installed
4. Check that the Flask app can start successfully

The seeding script provides detailed progress information and error messages to help diagnose any issues.
