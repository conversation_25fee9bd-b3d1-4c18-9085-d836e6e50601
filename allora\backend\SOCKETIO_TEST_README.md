# SocketIO Comprehensive Test Suite

This directory contains a comprehensive test suite for testing the Flask-SocketIO integration in the Allora backend.

## 📋 Overview

The test suite thoroughly validates:

- ✅ **Connection Testing** - Basic and authenticated connections
- ✅ **Event Handling** - All custom SocketIO events (ping/pong, heartbeat, subscriptions)
- ✅ **Real-time Features** - Inventory updates, cart sync, order notifications
- ✅ **API Integration** - REST endpoints that trigger SocketIO events
- ✅ **Room Management** - User rooms, admin rooms, guest rooms
- ✅ **Error Handling** - Edge cases and malformed data
- ✅ **Performance** - Concurrent connections and stress testing
- ✅ **Admin Features** - Admin-specific notifications and events
- ✅ **Reconnection** - Automatic reconnection functionality

## 🚀 Quick Start

### Option 1: Automated Test Runner (Recommended)

```bash
# Run with automatic server detection/startup
python run_socketio_tests.py

# Run against specific server
python run_socketio_tests.py http://localhost:5000
python run_socketio_tests.py http://*************:5000
```

### Option 2: Manual Test Execution

```bash
# 1. Ensure server is running
python run_hybrid_server.py
# or
python run_with_waitress.py

# 2. Run tests in another terminal
python test_socketio_comprehensive.py
```

## 📦 Dependencies

The test suite requires minimal additional dependencies:

```bash
# Install test dependencies (if not already installed)
pip install -r test_requirements.txt

# Or install manually
pip install requests python-socketio
```

## 📊 Test Results

The test suite generates multiple outputs:

1. **Console Output** - Real-time test progress and results
2. **Log File** - `socketio_test_results.log` with detailed logs
3. **JSON Report** - `socketio_test_report.json` with comprehensive results

### Sample Output

```
🧪 Allora SocketIO Comprehensive Test Suite
==================================================
🎯 Target Server: http://localhost:5000
📅 Test Started: 2025-07-16 10:30:00

📡 Phase 1: Basic Connectivity Tests
----------------------------------------
✅ PASS Server Availability (0.12s): Server response: 200
✅ PASS SocketIO Test Page (0.08s): SocketIO test page: 200

🔌 Phase 2: Connection Tests
----------------------------------------
✅ PASS Basic Connection (1.05s): Guest connection successful
✅ PASS Authenticated Connections (2.34s): 3/3 authenticated connections successful

📨 Phase 3: Event Handling Tests
----------------------------------------
✅ PASS Ping-Pong Test (2.01s): Pong received
✅ PASS Subscription Test (2.02s): Subscription confirmed
✅ PASS Heartbeat Test (2.01s): Heartbeat acknowledged

🌐 Phase 4: API Integration Tests
----------------------------------------
✅ PASS API Broadcast Test (2.15s): API response: 200, Broadcast received: True
✅ PASS API User Notification Test (2.12s): API response: 200, Notification received: True
✅ PASS API Inventory Update Test (2.18s): API response: 200, Update received: True
✅ PASS API Cart Update Test (2.14s): API response: 200, Update received: True
✅ PASS API Order Update Test (2.16s): API response: 200, Update received: True

⚡ Phase 5: Advanced Features Tests
----------------------------------------
✅ PASS Admin Features Test (2.20s): API response: 200, Admin notification received: True
✅ PASS Error Handling Test (3.05s): 3/3 error handling tests passed
✅ PASS Reconnection Test (4.12s): Reconnection successful

🏃 Phase 6: Performance Tests
----------------------------------------
✅ PASS Concurrent Connections Test (8.45s): 10/10 concurrent connections successful, 10/10 responses received

📊 COMPREHENSIVE SOCKETIO TEST REPORT
============================================================
📈 Total Tests: 13
✅ Passed: 13
❌ Failed: 0
📊 Success Rate: 100.0%
⏱️  Total Duration: 35.67s

🔌 Connection Statistics:
   Successful: 23
   Failed: 0
   Disconnections: 23
   Reconnections: 1

📨 Events Received: 45

🎉 All tests completed successfully!
```

## 🔧 Configuration

### Environment Variables

- `SOCKETIO_TEST_SERVER` - Target server URL (default: http://localhost:5000)

### Test Customization

You can modify the test parameters in `test_socketio_comprehensive.py`:

```python
# Number of concurrent connections to test
self.test_concurrent_connections(20)  # Default: 10

# Test data
self.test_data = {
    'users': [...],      # Test users
    'products': [...],   # Test product IDs
    'orders': [...]      # Test order IDs
}
```

## 🐛 Troubleshooting

### Common Issues

1. **Server Not Running**
   ```
   ❌ Server is not accessible (connection refused)
   ```
   **Solution**: Start the server first with `python run_hybrid_server.py`

2. **Missing Dependencies**
   ```
   ❌ Missing required packages: python-socketio
   ```
   **Solution**: Install with `pip install python-socketio`

3. **Connection Timeouts**
   ```
   ❌ Connection error: timeout
   ```
   **Solution**: Check server load, increase timeout, or reduce concurrent connections

4. **Redis Connection Issues**
   ```
   ⚠️ Redis connection failed - pub/sub disabled
   ```
   **Solution**: This is expected if Redis is not running. Tests will still work without Redis.

### Debug Mode

For more detailed debugging, modify the logging level:

```python
logging.basicConfig(level=logging.DEBUG)
```

## 📁 Files

- `test_socketio_comprehensive.py` - Main test suite
- `run_socketio_tests.py` - Automated test runner
- `test_requirements.txt` - Test dependencies
- `SOCKETIO_TEST_README.md` - This documentation

## 🎯 Test Coverage

The test suite covers all SocketIO functionality:

### Connection Management
- [x] Guest connections
- [x] Authenticated user connections  
- [x] Admin connections
- [x] Reconnection handling
- [x] Concurrent connections

### Event Handlers
- [x] `connect` / `disconnect`
- [x] `ping` / `pong`
- [x] `heartbeat` / `heartbeat_ack`
- [x] `subscribe` / `subscribed`

### Real-time Events
- [x] `inventory_update`
- [x] `price_update`
- [x] `cart_update`
- [x] `order_status_update`
- [x] `notification`
- [x] `broadcast`
- [x] `admin_notification`

### API Endpoints
- [x] `/api/socketio/broadcast`
- [x] `/api/socketio/notify-user/<user_id>`
- [x] `/api/socketio/events/inventory-update`
- [x] `/api/socketio/events/cart-update`
- [x] `/api/socketio/events/order-update`
- [x] `/api/socketio/admin-notification`

### Error Handling
- [x] Invalid event data
- [x] Malformed JSON
- [x] Large data payloads
- [x] Network interruptions

## 🏆 Success Criteria

A successful test run should show:
- ✅ 100% test pass rate
- ✅ All connections successful
- ✅ All events received correctly
- ✅ API endpoints responding properly
- ✅ No critical errors in logs

## 📞 Support

If you encounter issues with the test suite:

1. Check the generated log files for detailed error information
2. Verify the server is running and accessible
3. Ensure all dependencies are installed
4. Check the server logs for any backend errors

The test suite is designed to be comprehensive and should catch any issues with the SocketIO implementation.
