# SocketIO Test Dependencies
# ==========================
# 
# These are the minimal dependencies required to run the SocketIO test suite.
# Most of these should already be installed if you have the main backend requirements.

# Core testing dependencies
requests>=2.32.0
python-socketio>=5.11.0

# Optional: Enhanced testing capabilities
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-timeout>=2.1.0

# Optional: Test reporting
pytest-html>=3.1.0
pytest-json-report>=1.5.0

# Optional: Performance testing
psutil>=5.9.0
