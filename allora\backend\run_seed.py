#!/usr/bin/env python3
"""
Simple runner script for the database seeding
==============================================

This script provides an easy way to run the database seeding with proper
environment setup and error handling.

Usage:
    python run_seed.py

Or from the command line:
    python -m run_seed
"""

import os
import sys
import subprocess

def check_requirements():
    """Check if required packages are installed"""
    required_packages = ['flask', 'sqlalchemy', 'faker', 'mysql-connector-python']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   • {package}")
        print("\nInstall missing packages with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_database_connection():
    """Check if database is accessible"""
    try:
        from app import app, db
        
        with app.app_context():
            # Try to connect to database
            db.engine.execute('SELECT 1')
            print("✅ Database connection successful")
            return True
            
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("\nPlease ensure:")
        print("• MySQL server is running")
        print("• Database 'allora_db' exists")
        print("• Database credentials in .env are correct")
        return False

def run_seeding():
    """Run the database seeding script"""
    print("🌱 Allora Sustainable E-commerce Database Seeding")
    print("=" * 50)
    
    # Check requirements
    print("🔍 Checking requirements...")
    if not check_requirements():
        return False
    
    # Check database connection
    print("🔗 Checking database connection...")
    if not check_database_connection():
        return False
    
    # Run seeding
    print("🚀 Starting database seeding...")
    try:
        from seed_database import main
        result = main()
        return result == 0
        
    except Exception as e:
        print(f"❌ Seeding failed: {e}")
        return False

def main():
    """Main function"""
    try:
        success = run_seeding()
        
        if success:
            print("\n🎉 Database seeding completed successfully!")
            print("\nYour sustainable e-commerce platform is now ready with:")
            print("• 120+ sustainable products across 10 categories")
            print("• 50+ users with realistic profiles")
            print("• 10+ sustainable sellers")
            print("• 200+ orders with purchase history")
            print("• 500+ product reviews and ratings")
            print("• 150+ community posts with engagement")
            print("• Complete supporting data (shipping, payments, etc.)")
            print("\n🌍 Start your Flask app to explore the seeded data!")
            return 0
        else:
            print("\n❌ Database seeding failed!")
            print("Please check the error messages above and try again.")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️  Seeding interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
